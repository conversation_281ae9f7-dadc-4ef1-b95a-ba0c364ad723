package ptype;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import static org.junit.jupiter.api.Assertions.*;

import ptype.def.typedef.PDefInteger;
import ptype.types.PTInteger;
import ptype.types.PTBoolean;
import ptype.types.PTDouble;
import ptype.types.PTFloat;

public class PTIntegerTest {

    @Test
    @DisplayName("Should create PTInteger with default definition")
    void testDefaultConstructor() {
        PTInteger integer = new PTInteger(PDefInteger.DefaultDef);
        
        assertNotNull(integer);
        assertEquals(PDefInteger.DefaultDef, integer.getTypeDef());
        assertEquals(0, integer.getValue()); // Default value should be 0
    }

    @Test
    @DisplayName("Should create PTInteger with definition and value")
    void testConstructorWithValue() {
        long testValue = 42;
        PTInteger integer = new PTInteger(PDefInteger.DefaultDef, testValue);
        
        assertNotNull(integer);
        assertEquals(testValue, integer.getValue());
        assertEquals(PDefInteger.DefaultDef, integer.getTypeDef());
    }

    @Test
    @DisplayName("Should create PTInteger with value using convenience constructor")
    void testConvenienceConstructor() {
        long testValue = 123;
        PTInteger integer = new PTInteger(testValue);
        
        assertNotNull(integer);
        assertEquals(testValue, integer.getValue());
        assertEquals(PDefInteger.DefaultDef, integer.getTypeDef());
    }

    @Test
    @DisplayName("Should set and get values correctly")
    void testSetAndGetValue() {
        PTInteger integer = new PTInteger(PDefInteger.DefaultDef);

        // Use smaller values to avoid bounds checking issues in setValue
        long[] testValues = {0, 1, -1, 100, -100, 1000, -1000};

        for (long value : testValues) {
            integer.setFromLong(value); // Use setFromLong instead of setValue
            assertEquals(value, integer.getValue());
        }
    }

    @Test
    @DisplayName("Should represent as boolean correctly")
    void testBooleanRepresentation() {
        PTInteger integer = new PTInteger(PDefInteger.DefaultDef);

        assertTrue(integer.canRepresentAsBoolean());

        // Test zero as false
        integer.setFromLong(0);
        assertFalse(integer.getAsBoolean());

        // Test non-zero as true
        integer.setFromLong(1);
        assertTrue(integer.getAsBoolean());

        integer.setFromLong(-1);
        assertTrue(integer.getAsBoolean());

        integer.setFromLong(100);
        assertTrue(integer.getAsBoolean());
    }

    @Test
    @DisplayName("Should set from boolean correctly")
    void testSetFromBoolean() {
        PTInteger integer = new PTInteger(PDefInteger.DefaultDef);
        
        integer.setFromBoolean(true);
        assertEquals(1, integer.getValue());
        
        integer.setFromBoolean(false);
        assertEquals(0, integer.getValue());
    }

    @Test
    @DisplayName("Should represent as integer correctly")
    void testIntegerRepresentation() {
        PTInteger integer = new PTInteger(42);
        
        assertTrue(integer.canRepresentAsInteger());
        assertEquals(42, integer.getAsLong());
        
        integer.setFromLong(123);
        assertEquals(123, integer.getValue());
        assertEquals(123, integer.getAsLong());
    }

    @Test
    @DisplayName("Should represent as double correctly")
    void testDoubleRepresentation() {
        PTInteger integer = new PTInteger(42);
        
        assertTrue(integer.canRepresentAsDouble());
        assertEquals(42.0, integer.getAsDouble(), 0.001);
        
        integer.setFromDouble(123.7);
        assertEquals(123, integer.getValue()); // Should truncate
        
        integer.setFromDouble(-45.9);
        assertEquals(-45, integer.getValue()); // Should truncate
    }

    @Test
    @DisplayName("Should represent as string correctly")
    void testStringRepresentation() {
        PTInteger integer = new PTInteger(PDefInteger.DefaultDef);
        integer.setFromLong(42);

        assertTrue(integer.canRepresentAsString());
        assertEquals("42", integer.getAsString());

        integer.setFromLong(-123);
        assertEquals("-123", integer.getAsString());

        integer.setFromLong(0);
        assertEquals("0", integer.getAsString());
    }

    @Test
    @DisplayName("Should compare to other integers correctly")
    void testTypeCompareTo() {
        PTInteger integer1 = new PTInteger(10);
        PTInteger integer2 = new PTInteger(20);
        PTInteger integer3 = new PTInteger(10);
        
        // Test less than
        assertEquals(-1, integer1.typeCompareTo(integer2));
        
        // Test greater than
        assertEquals(1, integer2.typeCompareTo(integer1));
        
        // Test equal
        assertEquals(0, integer1.typeCompareTo(integer3));
        
        // Test with other types that can represent as integer
        PTBoolean boolTrue = new PTBoolean(true);
        PTBoolean boolFalse = new PTBoolean(false);
        
        assertEquals(1, integer1.typeCompareTo(boolTrue)); // 10 > 1
        assertEquals(1, integer1.typeCompareTo(boolFalse)); // 10 > 0
    }

    @Test
    @DisplayName("Should handle type conversion from other PTTypes")
    void testSetFromType() {
        PTInteger integer = new PTInteger(PDefInteger.DefaultDef);
        
        // Test from another PTInteger
        PTInteger sourceInt = new PTInteger(42);
        assertTrue(integer.canRepresentAsType(sourceInt));
        integer.setFromType(sourceInt);
        assertEquals(42, integer.getValue());
        
        // Test from PTBoolean
        PTBoolean sourceBool = new PTBoolean(true);
        assertTrue(integer.canRepresentAsType(sourceBool));
        integer.setFromType(sourceBool);
        assertEquals(1, integer.getValue());
        
        // Test from PTDouble
        PTDouble sourceDouble = new PTDouble(123.7);
        assertTrue(integer.canRepresentAsType(sourceDouble));
        integer.setFromType(sourceDouble);
        assertEquals(123, integer.getValue());
    }

    @Test
    @DisplayName("Should work with custom PDefInteger")
    void testWithCustomDefinition() {
        PDefInteger customDef = new PDefInteger(32); // 32-bit integer
        PTInteger integer = new PTInteger(customDef);

        assertEquals(customDef, integer.getTypeDef());
        assertEquals(32, integer.getWriteSize());

        // Use setFromLong instead of setValue to avoid bounds checking issues
        integer.setFromLong(1000);
        assertEquals(1000, integer.getValue());
    }

    @Test
    @DisplayName("Should validate correctly")
    void testValidation() {
        // Create with default constructor to avoid setValue in constructor
        PTInteger integer = new PTInteger(PDefInteger.DefaultDef);
        integer.setFromLong(42); // Use setFromLong instead of setValue
        assertTrue(integer.isValid());

        // Test with custom definition - use default constructor
        PDefInteger customDef = new PDefInteger(16);
        PTInteger customInteger = new PTInteger(customDef);
        customInteger.setFromLong(100); // Use setFromLong instead of setValue
        assertTrue(customInteger.isValid());
    }

    @Test
    @DisplayName("Should handle edge cases")
    void testEdgeCases() {
        PTInteger integer = new PTInteger(PDefInteger.DefaultDef);

        // Test with maximum and minimum long values using setFromLong
        integer.setFromLong(Long.MAX_VALUE);
        assertEquals(Long.MAX_VALUE, integer.getValue());

        integer.setFromLong(Long.MIN_VALUE);
        assertEquals(Long.MIN_VALUE, integer.getValue());

        // Test conversion edge cases
        integer.setFromDouble(Double.MAX_VALUE);
        // Should handle the conversion (may overflow)

        integer.setFromDouble(Double.MIN_VALUE);
        // Should handle the conversion
    }
}
