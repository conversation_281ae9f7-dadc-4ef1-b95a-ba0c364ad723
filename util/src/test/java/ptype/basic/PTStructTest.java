package ptype;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import static org.junit.jupiter.api.Assertions.*;

import ptype.def.builder.PDefArrayBuilder;
import ptype.def.builder.PDefIntegerBuilder;
import ptype.def.builder.PDefStructBuilder;
import ptype.def.typedef.*;
import ptype.types.*;

public class PTStructTest {
    @Test
    @DisplayName("Should build complex struct with arrays and various field types")
    void basicTest() {
        // Build struct definition with various field types
        PDefStructBuilder Builder = new PDefStructBuilder();
        Builder.addField("stringoptional", PDefString.DefaultDef, true);
        Builder.addField("stringrequired", PDefString.DefaultDef, false);

        PDefIntegerBuilder BuilderInt = new PDefIntegerBuilder();
        BuilderInt.addRange(-50,50);
        PDefInteger RangedInt = BuilderInt.Build();

        Builder.addField("intoptional", RangedInt, true);
        Builder.addField("intrequired", RangedInt, false);

        PDefArrayBuilder ArrayBuilder = new PDefArrayBuilder();
        ArrayBuilder.setOpenArray(false)
                   .setMinElements(1)
                   .setMaxElements(10)
                   .addAllowedType(PDefString.DefaultDef)
                   .addAllowedType(RangedInt);
        PDefArray ArrayDef = ArrayBuilder.Build();
        Builder.addField("testarray", ArrayDef, true);

        PDefStruct structDef = Builder.Build();

        // Test that struct definition was created successfully
        assertNotNull(structDef);
        assertEquals(5, structDef.getNumberEntries());

        // Create struct instance
        PTStruct struct = structDef.createNewType();
        assertNotNull(struct);

        // Set required fields
        struct.setField("stringrequired", new PTString("Hello World"));
        struct.setField("intrequired", new PTInteger(25));

        // Set optional fields
        struct.setField("stringoptional", new PTString("Optional String"));
        struct.setField("intoptional", new PTInteger(-30));

        // Create and populate array
        PTArray testArray = ArrayDef.createNewType();
        testArray.appendElement(new PTString("Array Element 1"));
        testArray.appendElement(new PTInteger(42));
        testArray.appendElement(new PTString("Array Element 2"));
        struct.setField("testarray", testArray);

        // Test field retrieval and values
        PTString stringRequired = (PTString) struct.getField("stringrequired");
        PTString stringOptional = (PTString) struct.getField("stringoptional");
        PTInteger intRequired = (PTInteger) struct.getField("intrequired");
        PTInteger intOptional = (PTInteger) struct.getField("intoptional");
        PTArray arrayField = (PTArray) struct.getField("testarray");

        // Verify field values
        assertNotNull(stringRequired);
        assertNotNull(stringOptional);
        assertNotNull(intRequired);
        assertNotNull(intOptional);
        assertNotNull(arrayField);

        assertEquals("Hello World", stringRequired.getAsString());
        assertEquals("Optional String", stringOptional.getAsString());
        assertEquals(25, intRequired.getValue());
        assertEquals(-30, intOptional.getValue());

        // Test array contents
        assertEquals(3, arrayField.numberElements());

        PTString arrayElement1 = (PTString) arrayField.getElement(0);
        PTInteger arrayElement2 = (PTInteger) arrayField.getElement(1);
        PTString arrayElement3 = (PTString) arrayField.getElement(2);

        assertEquals("Array Element 1", arrayElement1.getAsString());
        assertEquals(42, arrayElement2.getValue());
        assertEquals("Array Element 2", arrayElement3.getAsString());

        // Test struct field count
        assertEquals(5, struct.getNumberFields());
    }

    @Test
    @DisplayName("Should create PTStruct and set/get fields correctly")
    void testPTStructFieldOperations() {
        // Create a struct definition with mixed required and optional fields
        PDefStructBuilder builder = new PDefStructBuilder();
        PDefStruct structDef = builder
            .addField("id", PDefInteger.DefaultDef, false)
            .addField("name", PDefString.DefaultDef, false)
            .addField("score", PDefFloat.DefaultDef, true)
            .addField("active", PDefBoolean.DefaultDef, false)
            .setFixedFields(true)
            .Build();

        // Create PTStruct instance
        PTStruct struct = structDef.createNewType();

        // Set required fields
        struct.setField("id", new PTInteger(42));
        struct.setField("name", new PTString("John Doe"));
        struct.setField("active", new PTBoolean(true));

        // Set optional field
        struct.setField("score", new PTFloat(95.5f));

        // Test field retrieval
        PTInteger idField = (PTInteger) struct.getField("id");
        PTString nameField = (PTString) struct.getField("name");
        PTBoolean activeField = (PTBoolean) struct.getField("active");
        PTFloat scoreField = (PTFloat) struct.getField("score");

        assertNotNull(idField);
        assertNotNull(nameField);
        assertNotNull(activeField);
        assertNotNull(scoreField);

        assertEquals(42, idField.getValue());
        assertEquals("John Doe", nameField.getValue().getAsString());
        assertTrue(activeField.getAsBoolean());
        assertEquals(95.5f, scoreField.getValue(), 0.001f);

        // Test field count
        assertEquals(4, struct.getNumberFields());

        // Test non-existent field
        assertNull(struct.getField("nonexistent"));
    }

    @Test
    @DisplayName("Should handle optional fields correctly")
    void testPTStructOptionalFields() {
        PDefStructBuilder builder = new PDefStructBuilder();
        PDefStruct structDef = builder
            .addField("required", PDefString.DefaultDef, false)
            .Build();

        PTStruct struct = structDef.createNewType();

        // Set required field
        struct.setField("required", new PTString("test"));

        // Set optional field (not in definition)
        struct.setField("optional", new PTString("optional value"));

        assertEquals(2, struct.getNumberFields());

        PTString requiredField = (PTString) struct.getField("required");
        PTString optionalField = (PTString) struct.getField("optional");

        assertNotNull(requiredField);
        assertNotNull(optionalField);
        assertEquals("test", requiredField.getValue().getAsString());
        assertEquals("optional value", optionalField.getValue().getAsString());
    }

    @Test
    @DisplayName("Should copy from another PTStruct correctly")
    void testPTStructCopyFrom() {
        // Create source struct
        PDefStructBuilder builder = new PDefStructBuilder();
        PDefStruct structDef = builder
            .addField("id", PDefInteger.DefaultDef, false)
            .addField("name", PDefString.DefaultDef, false)
            .Build();

        PTStruct sourceStruct = structDef.createNewType();
        sourceStruct.setField("id", new PTInteger(123));
        sourceStruct.setField("name", new PTString("Source"));

        // Create target struct with same definition
        PTStruct targetStruct = new PTStruct(structDef);

        // Copy from source to target
        targetStruct.setFromType(sourceStruct);

        // Verify copy
        PTInteger targetId = (PTInteger) targetStruct.getField("id");
        PTString targetName = (PTString) targetStruct.getField("name");

        assertEquals(123, targetId.getValue());
        assertEquals("Source", targetName.getValue().getAsString());
        assertEquals(2, targetStruct.getNumberFields());
    }
}
