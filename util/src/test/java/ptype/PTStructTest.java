package ptype;

import org.junit.jupiter.api.Test;
import ptype.def.builder.PDefIntegerBuilder;
import ptype.def.builder.PDefStructBuilder;
import ptype.def.typedef.PDefInteger;

public class PTStructTest {
    @Test
    void basicTest() {
        PDefStructBuilder Builder = new PDefStructBuilder();
        Builder.addField("stringoptional", ptype.def.typedef.PDefString.DefaultDef, true);
        Builder.addField("stringrequired", ptype.def.typedef.PDefString.DefaultDef, false);

        PDefIntegerBuilder BuilderInt = new PDefIntegerBuilder();
        BuilderInt.addRange(-50,50);
        PDefInteger RangedInt = BuilderInt.Build();

        Builder.addField("intoptional", RangedInt, true);
        Builder.addField("intrequired", RangedInt, false);

    }
}
