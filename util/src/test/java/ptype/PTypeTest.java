package ptype;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import static org.junit.jupiter.api.Assertions.*;

import ptype.def.typedef.*;
import ptype.types.*;
import ptype.def.builder.PDefStructBuilder;

public class PTypeTest {
    
    @Test
    @DisplayName("Should create PTStruct and set/get fields correctly")
    void testPTStructFieldOperations() {
        // Create a struct definition with mixed required and optional fields
        PDefStructBuilder builder = new PDefStructBuilder();
        PDefStruct structDef = builder
            .addField("id", PDefInteger.DefaultDef, false)
            .addField("name", PDefString.DefaultDef, false)
            .addField("score", PDefFloat.DefaultDef, true)
            .addField("active", PDefBoolean.DefaultDef, false)
            .setFixedFields(true)
            .Build();
        
        // Create PTStruct instance
        PTStruct struct = structDef.createNewType();
        
        // Set required fields
        struct.setField("id", new PTInteger(42));
        struct.setField("name", new PTString("John Doe"));
        struct.setField("active", new PTBoolean(true));
        
        // Set optional field
        struct.setField("score", new PTFloat(95.5f));
        
        // Test field retrieval
        PTInteger idField = (PTInteger) struct.getField("id");
        PTString nameField = (PTString) struct.getField("name");
        PTBoolean activeField = (PTBoolean) struct.getField("active");
        PTFloat scoreField = (PTFloat) struct.getField("score");
        
        assertNotNull(idField);
        assertNotNull(nameField);
        assertNotNull(activeField);
        assertNotNull(scoreField);
        
        assertEquals(42, idField.getValue());
        assertEquals("John Doe", nameField.getValue().getAsString());
        assertTrue(activeField.getAsBoolean());
        assertEquals(95.5f, scoreField.getValue(), 0.001f);
        
        // Test field count
        assertEquals(4, struct.getNumberFields());
        
        // Test non-existent field
        assertNull(struct.getField("nonexistent"));
    }
    
    @Test
    @DisplayName("Should handle optional fields correctly")
    void testPTStructOptionalFields() {
        PDefStructBuilder builder = new PDefStructBuilder();
        PDefStruct structDef = builder
            .addField("required", PDefString.DefaultDef, false)
            .Build();
        
        PTStruct struct = structDef.createNewType();
        
        // Set required field
        struct.setField("required", new PTString("test"));
        
        // Set optional field (not in definition)
        struct.setField("optional", new PTString("optional value"));
        
        assertEquals(2, struct.getNumberFields());
        
        PTString requiredField = (PTString) struct.getField("required");
        PTString optionalField = (PTString) struct.getField("optional");
        
        assertNotNull(requiredField);
        assertNotNull(optionalField);
        assertEquals("test", requiredField.getValue().getAsString());
        assertEquals("optional value", optionalField.getValue().getAsString());
    }
    
    @Test
    @DisplayName("Should copy from another PTStruct correctly")
    void testPTStructCopyFrom() {
        // Create source struct
        PDefStructBuilder builder = new PDefStructBuilder();
        PDefStruct structDef = builder
            .addField("id", PDefInteger.DefaultDef, false)
            .addField("name", PDefString.DefaultDef, false)
            .Build();
        
        PTStruct sourceStruct = structDef.createNewType();
        sourceStruct.setField("id", new PTInteger(123));
        sourceStruct.setField("name", new PTString("Source"));
        
        // Create target struct with same definition
        PTStruct targetStruct = new PTStruct(structDef);
        
        // Copy from source to target
        targetStruct.setFromType(sourceStruct);
        
        // Verify copy
        PTInteger targetId = (PTInteger) targetStruct.getField("id");
        PTString targetName = (PTString) targetStruct.getField("name");
        
        assertEquals(123, targetId.getValue());
        assertEquals("Source", targetName.getValue().getAsString());
        assertEquals(2, targetStruct.getNumberFields());
    }
}
